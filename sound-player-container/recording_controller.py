#!/usr/bin/env python3
"""Recording Controller - Handles recording based on signal events."""

import logging
import threading
import time
from pathlib import Path
from typing import List, Optional
from collections import deque
from datetime import datetime
import pytz

import numpy as np
from pydub import AudioSegment

from unified_signal_detector import Signal<PERSON>vent
from audio_event_manager import AudioEventSubscriber, AudioEvent
from audio_config import AudioConfig

logger = logging.getLogger(__name__)


class RecordingController(AudioEventSubscriber):
    """Handles recording based on signal events."""

    def __init__(self, storage_path: str, pre_roll_seconds: float, post_roll_seconds: float,
                 min_segment_duration: float, max_segment_duration: float,
                 merge_gap_threshold: float, api_upload_manager=None):
        self.storage_path = Path(storage_path)
        self.pre_roll_seconds = pre_roll_seconds
        self.post_roll_seconds = post_roll_seconds
        self.min_segment_duration = min_segment_duration
        self.max_segment_duration = max_segment_duration
        self.merge_gap_threshold = merge_gap_threshold
        self.api_upload_manager = api_upload_manager

        # State
        self.is_recording = False
        self.recording_start_time: Optional[float] = None
        self.last_signal_end_time: Optional[float] = None
        self.audio_buffer: List[np.ndarray] = []
        self.buffer_timestamps: List[float] = []
        self.sample_rate: Optional[int] = None

        # Pre-roll buffer with size limit
        # Calculate max chunks for pre-roll duration (e.g., 0.5s at 48kHz/1024 = ~24 chunks)
        self.max_pre_roll_chunks = max(100, int((self.pre_roll_seconds * 48000) / 1024))
        self.pre_roll_buffer = deque(maxlen=self.max_pre_roll_chunks)
        self.pre_roll_timestamps = deque(maxlen=self.max_pre_roll_chunks)

        self._lock = threading.Lock()
        self.storage_path.mkdir(parents=True, exist_ok=True)

        # Timezone handling
        try:
            self.station_tz = pytz.timezone(AudioConfig.STATION_TZ)
        except pytz.exceptions.UnknownTimeZoneError:
            logger.warning(f"Unknown timezone '{AudioConfig.STATION_TZ}', using UTC")
            self.station_tz = pytz.UTC
    
    def on_start(self) -> None:
        pass

    def on_stop(self) -> None:
        with self._lock:
            if self.is_recording:
                self._finalize_recording()

    def on_signal_detected(self, signal_event: SignalEvent) -> None:
        """Handle signal event for recording."""
        with self._lock:
            # Check merge with existing recording
            if self.is_recording and self.last_signal_end_time:
                gap = signal_event.start_time - self.last_signal_end_time
                if gap <= self.merge_gap_threshold:
                    # Merge: extend the recording with this signal
                    self.last_signal_end_time = signal_event.end_time
                    logger.debug(f"Merging signal (gap: {gap:.3f}s <= {self.merge_gap_threshold}s)")
                    return
                else:
                    # Gap too large, finalize current recording and start new one
                    logger.debug(f"Gap too large ({gap:.3f}s), finalizing current recording")
                    self._finalize_recording()

            # Start new recording
            if not self.is_recording:
                self._start_recording(signal_event.start_time)

            self.last_signal_end_time = signal_event.end_time
    
    def on_audio_chunk(self, event: AudioEvent) -> None:
        """Process audio chunk."""
        with self._lock:
            if self.sample_rate is None:
                self.sample_rate = event.sample_rate

            # Update pre-roll buffer
            self.pre_roll_buffer.append(event.chunk)
            self.pre_roll_timestamps.append(event.timestamp)
            while len(self.pre_roll_buffer) > self.max_pre_roll_chunks:
                self.pre_roll_buffer.popleft()
                self.pre_roll_timestamps.popleft()

            # Add to recording if active
            if self.is_recording:
                self.audio_buffer.append(event.chunk)
                self.buffer_timestamps.append(event.timestamp)

                # Check if we should finalize recording
                # Only finalize if we've waited both POST_ROLL_SECONDS + MERGE_GAP_THRESHOLD
                # This ensures we don't finalize too early if another signal might come
                if (self.last_signal_end_time and
                    event.timestamp - self.last_signal_end_time >= (self.post_roll_seconds + self.merge_gap_threshold)):
                    logger.debug(f"Finalizing recording: {event.timestamp - self.last_signal_end_time:.3f}s since last signal")
                    self._finalize_recording()

                # Check max duration
                if (self.recording_start_time and
                    event.timestamp - self.recording_start_time >= self.max_segment_duration):
                    self._finalize_recording()
    
    def _start_recording(self, signal_start_time: float) -> None:
        """Start recording with pre-roll."""
        self.is_recording = True
        self.recording_start_time = signal_start_time - self.pre_roll_seconds
        self.audio_buffer = []
        self.buffer_timestamps = []

        # Add pre-roll data
        cutoff = signal_start_time - self.pre_roll_seconds
        for chunk, timestamp in zip(self.pre_roll_buffer, self.pre_roll_timestamps):
            if timestamp >= cutoff:
                self.audio_buffer.append(chunk)
                self.buffer_timestamps.append(timestamp)
    
    def _finalize_recording(self) -> None:
        """Finalize and save recording."""
        if not self.is_recording or not self.audio_buffer:
            self._reset_recording_state()
            return

        try:
            duration = self.buffer_timestamps[-1] - self.buffer_timestamps[0]
            if duration >= self.min_segment_duration:
                self._save_recording()
        except Exception as e:
            logger.error(f"Error saving recording: {e}")
        finally:
            self._reset_recording_state()
    
    def _save_recording(self) -> None:
        """Save the current recording to file."""
        if not self.audio_buffer or not self.sample_rate:
            return
        
        # Concatenate all audio chunks
        audio_data = np.concatenate(self.audio_buffer)
        
        # Convert to AudioSegment
        audio_segment = AudioSegment(
            audio_data.tobytes(),
            frame_rate=self.sample_rate,
            sample_width=audio_data.dtype.itemsize,
            channels=1
        )
        
        # Generate timezone-aware filename and path
        # Convert to station timezone
        utc_start_time = datetime.fromtimestamp(self.recording_start_time, tz=pytz.UTC)
        local_start_time = utc_start_time.astimezone(self.station_tz)

        # Calculate duration in milliseconds
        duration = self.buffer_timestamps[-1] - self.buffer_timestamps[0]
        duration_ms = int(duration * 1000)

        # Generate filename: YYYYMMDD_HHMMSS_mmm_dxxxxxx.webm
        milliseconds = int((self.recording_start_time % 1) * 1000)
        filename = f"{local_start_time.strftime('%Y%m%d_%H%M%S')}_{milliseconds:03d}_d{duration_ms:06d}.webm"

        # Create folder structure: recordings/STATION/YYYY/MM/DD/HH/
        folder_path = (self.storage_path /
                      local_start_time.strftime('%Y') /
                      local_start_time.strftime('%m') /
                      local_start_time.strftime('%d') /
                      local_start_time.strftime('%H'))
        folder_path.mkdir(parents=True, exist_ok=True)

        filepath = folder_path / filename
        
        # Export as WebM with Opus codec (same settings as original AudioRecorder)
        audio_segment.export(
            str(filepath),
            format="webm",
            codec="libopus",
            parameters=[
                "-c:a", "libopus",
                "-b:a", "16k",
                "-vbr", "on",
                "-compression_level", "10",
                "-application", "voip",
                "-frame_duration", "60",
                "-cutoff", "8000",
                "-ac", "1",
                "-vn"
            ]
        )
        
        duration = self.buffer_timestamps[-1] - self.buffer_timestamps[0]
        logger.info(f"Recording saved: {filename} ({duration:.2f}s)")

        # Upload to API if configured
        if self.api_upload_manager:
            try:
                self.api_upload_manager.queue_upload(str(filepath), self.recording_start_time)
            except Exception as e:
                logger.error(f"Failed to upload recording to API: {e}")

    def _reset_recording_state(self) -> None:
        """Reset recording state and clear buffers."""
        self.is_recording = False
        self.recording_start_time = None
        self.last_signal_end_time = None
        # Clear buffers to free memory
        self.audio_buffer.clear()
        self.buffer_timestamps.clear()
        # Also clear pre-roll buffer periodically to prevent buildup
        if len(self.pre_roll_buffer) > self.max_pre_roll_chunks * 0.8:
            # Keep only the most recent chunks
            keep_count = self.max_pre_roll_chunks // 2
            self.pre_roll_buffer = deque(list(self.pre_roll_buffer)[-keep_count:], maxlen=self.max_pre_roll_chunks)
            self.pre_roll_timestamps = deque(list(self.pre_roll_timestamps)[-keep_count:], maxlen=self.max_pre_roll_chunks)
