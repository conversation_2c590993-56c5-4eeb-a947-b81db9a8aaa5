'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import Hls from 'hls.js'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Volume2,
  VolumeX,
  RefreshCw,
  AlertCircle,
  Radio,
} from 'lucide-react'
import { config, getHLSPlaylistUrl } from '@/lib/config'
import type { HLSPlayerProps } from '@/types'

export const HLSPlayer: React.FC<HLSPlayerProps> = ({
  stationId = config.player.defaultStationId,
  apiBaseUrl = config.api.baseUrl,
  autoPlay = true, // Live streams should always auto-play
  className = ''
}) => {
  const audioRef = useRef<HTMLAudioElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Simplified player state - only what's essential
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const playlistUrl = getHLSPlaylistUrl(stationId)

  // Use the simplified robust HLS configuration from config
  const getSimplifiedHLSConfig = useCallback(() => {
    return config.hls
  }, [])

  // Simplified robust player initialization
  const initializePlayer = useCallback(async () => {
    if (!audioRef.current) return

    const audio = audioRef.current
    setError(null)
    setIsLoading(true)

    // Clear any existing retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
      retryTimeoutRef.current = null
    }

    // Check HLS support
    if (Hls.isSupported()) {
      // Destroy existing HLS instance
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }

      // Create new HLS instance with simplified robust config
      const hls = new Hls(getSimplifiedHLSConfig())
      hlsRef.current = hls

      hls.loadSource(playlistUrl)
      hls.attachMedia(audio)

      // Simplified event handlers focused on robustness
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        setIsLoading(false)
        setError(null)
        if (autoPlay) {
          handlePlay()
        }
      })

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS Error:', data.type, data.details)

        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Network error - attempting recovery...')
              // Retry after a short delay
              retryTimeoutRef.current = setTimeout(() => {
                if (hlsRef.current) {
                  hlsRef.current.startLoad()
                }
              }, 1000)
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Media error - attempting recovery...')
              hls.recoverMediaError()
              break
            default:
              console.log('Unrecoverable error - reinitializing...')
              // Reinitialize the entire player after a delay
              retryTimeoutRef.current = setTimeout(() => {
                initializePlayer()
              }, 2000)
              break
          }
        }
      })

    } else if (audio.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari)
      audio.src = playlistUrl
      setIsLoading(false)
      if (autoPlay) {
        handlePlay()
      }
    } else {
      setError('HLS not supported in this browser')
      setIsLoading(false)
    }
  }, [playlistUrl, autoPlay, getSimplifiedHLSConfig])

  // Simplified play handler - robust and always tries to play
  const handlePlay = useCallback(async () => {
    if (!audioRef.current) return

    try {
      if (hlsRef.current) {
        hlsRef.current.startLoad()
      }
      await audioRef.current.play()
      setIsPlaying(true)
      setError(null)
    } catch (err) {
      console.warn('Autoplay blocked, will retry on user interaction:', err)
      setError('Click to start live audio')
    }
  }, [])

  // Retry play on any user interaction
  const retryPlayOnInteraction = useCallback(async () => {
    if (!isPlaying && !isLoading) {
      await handlePlay()
    }
  }, [isPlaying, isLoading, handlePlay])

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!audioRef.current) return
    const newMuted = !isMuted
    audioRef.current.muted = newMuted
    setIsMuted(newMuted)
  }, [isMuted])

  // Handle volume change
  const handleVolumeChange = useCallback((newVolume: number) => {
    if (!audioRef.current) return
    audioRef.current.volume = newVolume
    setVolume(newVolume)
    if (newVolume === 0) {
      setIsMuted(true)
    } else if (isMuted) {
      setIsMuted(false)
    }
  }, [isMuted])

  // Simplified refresh - just reinitialize the player
  const refreshPlayer = useCallback(async () => {
    setIsLoading(true)
    await initializePlayer()
  }, [initializePlayer])

  // Initialize on mount
  useEffect(() => {
    initializePlayer()

    return () => {
      // Cleanup
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [initializePlayer])

  // Simplified audio event listeners
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const handleLoadStart = () => setIsLoading(true)
    const handleCanPlay = () => setIsLoading(false)
    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    const handleVolumeChange = () => {
      setVolume(audio.volume)
      setIsMuted(audio.muted)
    }

    audio.addEventListener('loadstart', handleLoadStart)
    audio.addEventListener('canplay', handleCanPlay)
    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)
    audio.addEventListener('volumechange', handleVolumeChange)

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart)
      audio.removeEventListener('canplay', handleCanPlay)
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
      audio.removeEventListener('volumechange', handleVolumeChange)
    }
  }, [])

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Radio className="w-5 h-5" />
          AWOS Live Stream
        </CardTitle>
        <CardDescription>
          Live audio from Ridge Landing Airpark - Station {stationId}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Audio Element */}
        <audio
          ref={audioRef}
          className="hidden"
          preload="none"
        />

        {/* Simplified Player Controls */}
        <div className="flex items-center justify-center gap-6" onClick={retryPlayOnInteraction}>
          {/* Live Status Indicator */}
          <div className="flex items-center gap-3">
            <div className={`w-4 h-4 rounded-full ${isPlaying ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`} />
            <span className="text-lg font-medium">
              {isLoading ? 'Connecting...' : isPlaying ? 'LIVE' : 'Click to start'}
            </span>
          </div>

          {/* Volume Controls */}
          <div className="flex items-center gap-3">
            <Button
              onClick={toggleMute}
              variant="outline"
              size="sm"
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </Button>

            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-24"
            />
          </div>

          {/* Refresh Button */}
          <Button
            onClick={refreshPlayer}
            variant="outline"
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Simple Status Message */}
        <div className="text-center text-sm text-muted-foreground">
          {isLoading && 'Connecting to live stream...'}
          {!isLoading && !isPlaying && !error && 'Ready to play live audio'}
          {!isLoading && isPlaying && 'Streaming live audio'}
        </div>
      </CardContent>
    </Card>
  )
}
