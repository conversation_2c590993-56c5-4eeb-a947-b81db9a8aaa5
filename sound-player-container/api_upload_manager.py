#!/usr/bin/env python3
"""
API Upload Manager - Upload recordings via Weather API

This module provides an API-based upload manager that uploads recording files
to the weather API container instead of directly to S3. This removes the need
for AWS credentials on customer devices.
"""

import logging
import os
import time
import threading
import queue
from pathlib import Path
from typing import Optional, Dict, Any

import requests

logger = logging.getLogger(__name__)


class APIUploadManager:
    """
    Manages recording uploads via the Weather API container.
    
    Features:
    - Infinite retry with exponential backoff
    - Thread-safe upload queue
    - Configurable retry parameters
    - Proper error logging and monitoring
    - No AWS credentials required on device
    """
    
    def __init__(self,
                 api_base_url: str,
                 station_id: str,
                 max_retry_delay: float = 300.0,  # 5 minutes max delay
                 initial_retry_delay: float = 1.0,
                 retry_multiplier: float = 2.0,
                 max_concurrent_uploads: int = 3,
                 request_timeout: float = 60.0):
        """
        Initialize the API Upload Manager.
        
        Args:
            api_base_url: Base URL of the weather API (e.g., https://awosnew.skytraces.com)
            station_id: Station ID for uploads (e.g., "4FL5_122900")
            max_retry_delay: Maximum delay between retries (seconds)
            initial_retry_delay: Initial delay between retries (seconds)
            retry_multiplier: Multiplier for exponential backoff
            max_concurrent_uploads: Maximum concurrent upload threads
            request_timeout: HTTP request timeout (seconds)
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.station_id = station_id
        self.upload_url = f"{self.api_base_url}/recordings/upload"
        
        # Retry configuration
        self.max_retry_delay = max_retry_delay
        self.initial_retry_delay = initial_retry_delay
        self.retry_multiplier = retry_multiplier
        self.request_timeout = request_timeout
        
        # Threading and queue management
        self.upload_queue = queue.Queue(maxsize=50)  # Limit queue size
        self.max_concurrent_uploads = max_concurrent_uploads
        self.upload_threads = []
        self.shutdown_event = threading.Event()
        self.stats_lock = threading.Lock()
        self.dropped_uploads = 0
        
        # Upload statistics
        self.stats = {
            'total_queued': 0,
            'total_uploaded': 0,
            'total_failed': 0,
            'current_queue_size': 0,
            'active_uploads': 0,
            'last_upload_time': None,
            'start_time': time.time()
        }
        
        # Test API connectivity
        self._test_api_connectivity()
        
        # Start upload worker threads
        self._start_upload_workers()
    
    def _test_api_connectivity(self) -> None:
        """Test connectivity to the weather API."""
        try:
            health_url = f"{self.api_base_url}/health"
            response = requests.get(health_url, timeout=10)
            if response.status_code == 200:
                logger.info(f"API connectivity test successful: {self.api_base_url}")
            else:
                logger.warning(f"API health check returned {response.status_code}")
        except Exception as e:
            logger.warning(f"API connectivity test failed: {e}")
            # Don't raise here - uploads may still work
    
    def _start_upload_workers(self) -> None:
        """Start background upload worker threads."""
        for i in range(self.max_concurrent_uploads):
            thread = threading.Thread(
                target=self._upload_worker,
                name=f"APIUploadWorker-{i+1}",
                daemon=True
            )
            thread.start()
            self.upload_threads.append(thread)
        
        logger.info(f"Started {self.max_concurrent_uploads} API upload worker threads")
    
    def _upload_worker(self) -> None:
        """Background worker thread for processing uploads."""
        logger.debug(f"Upload worker {threading.current_thread().name} started")
        
        while not self.shutdown_event.is_set():
            try:
                # Get upload task from queue (with timeout)
                upload_task = self.upload_queue.get(timeout=1.0)
                
                with self.stats_lock:
                    self.stats['active_uploads'] += 1
                    self.stats['current_queue_size'] = self.upload_queue.qsize()
                
                try:
                    # Process upload with retry logic
                    self._process_upload_with_retry(upload_task)
                finally:
                    with self.stats_lock:
                        self.stats['active_uploads'] -= 1
                    self.upload_queue.task_done()
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Unexpected error in upload worker: {e}")
        
        logger.debug(f"Upload worker {threading.current_thread().name} stopped")
    
    def _process_upload_with_retry(self, upload_task: Dict[str, Any]) -> None:
        """Process an upload task with infinite retry logic."""
        file_path = Path(upload_task['file_path'])
        retry_count = 0
        delay = self.initial_retry_delay

        while not self.shutdown_event.is_set():
            try:
                # Check if file still exists before attempting upload
                if not file_path.exists():
                    logger.warning(f"File no longer exists, skipping upload: {file_path}")
                    return

                # Attempt the upload (no timestamp needed)
                self._upload_file(file_path)

                # Success!
                with self.stats_lock:
                    self.stats['total_uploaded'] += 1
                    self.stats['last_upload_time'] = time.time()

                logger.info(f"Successfully uploaded: {file_path.name} (attempts: {retry_count + 1})")
                return

            except Exception as e:
                retry_count += 1
                
                with self.stats_lock:
                    self.stats['total_failed'] += 1

                logger.warning(f"Upload attempt {retry_count} failed for {file_path.name}: {e}")

                # Wait before retry with exponential backoff
                if not self.shutdown_event.wait(delay):
                    delay = min(delay * self.retry_multiplier, self.max_retry_delay)
                else:
                    return  # Shutdown requested
    
    def _upload_file(self, file_path: Path) -> None:
        """Upload a single file to the API."""
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        try:
            file_size = file_path.stat().st_size
            logger.debug(f"Uploading {file_path.name} ({file_size} bytes) to API")

            # Prepare form data with proper file handling
            file_handle = None
            try:
                file_handle = open(file_path, 'rb')
                files = {
                    'file': (file_path.name, file_handle, 'video/webm')
                }
                data = {
                    'station_id': self.station_id
                }

                # Make the upload request
                response = requests.post(
                    self.upload_url,
                    files=files,
                    data=data,
                    timeout=self.request_timeout
                )
            finally:
                # Ensure file is always closed
                if file_handle:
                    file_handle.close()

            # Check response
            if response.status_code == 200:
                result = response.json()
                logger.debug(f"Upload successful: {result}")
            else:
                raise Exception(f"API returned {response.status_code}: {response.text}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"HTTP request failed: {e}")
        except Exception as e:
            raise Exception(f"Upload failed: {e}")
    
    def queue_upload(self, file_path: str, timestamp: Optional[float] = None) -> None:
        """
        Queue a file for upload to the API.

        Args:
            file_path: Path to the file to upload
            timestamp: File timestamp (defaults to current time)
        """
        if timestamp is None:
            timestamp = time.time()

        file_path_obj = Path(file_path)

        # Validate file exists before queueing
        if not file_path_obj.exists():
            logger.warning(f"Cannot queue non-existent file: {file_path}")
            return

        # Validate file is not empty
        if file_path_obj.stat().st_size == 0:
            logger.warning(f"Cannot queue empty file: {file_path}")
            return

        upload_task = {
            'file_path': str(file_path_obj)
        }

        try:
            self.upload_queue.put(upload_task, block=False)
        except queue.Full:
            self.dropped_uploads += 1
            logger.warning(f"Upload queue full, dropped upload: {file_path_obj.name} (total dropped: {self.dropped_uploads})")

        with self.stats_lock:
            self.stats['total_queued'] += 1
            self.stats['current_queue_size'] = self.upload_queue.qsize()

        logger.info(f"Queued for API upload: {file_path_obj.name}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current upload statistics."""
        with self.stats_lock:
            return self.stats.copy()
    
    def get_formatted_stats(self) -> str:
        """Get formatted upload statistics string."""
        stats = self.get_stats()
        uptime = time.time() - stats['start_time']
        
        return (f"API Upload Stats - "
                f"Queued: {stats['total_queued']}, "
                f"Uploaded: {stats['total_uploaded']}, "
                f"Failed: {stats['total_failed']}, "
                f"Queue: {stats['current_queue_size']}, "
                f"Active: {stats['active_uploads']}, "
                f"Uptime: {uptime:.1f}s")
    
    def shutdown(self) -> None:
        """Shutdown the upload manager and wait for threads to complete."""
        logger.info("Shutting down API upload manager...")

        self.shutdown_event.set()

        # Wait for all threads to complete
        for thread in self.upload_threads:
            thread.join(timeout=5.0)

        # Clear upload queue to free memory
        while not self.upload_queue.empty():
            try:
                self.upload_queue.get_nowait()
            except queue.Empty:
                break

        if self.dropped_uploads > 0:
            logger.info(f"API upload manager shutdown complete - Dropped {self.dropped_uploads} uploads due to queue full")
        else:
            logger.info("API upload manager shutdown complete")
