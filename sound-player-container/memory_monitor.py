#!/usr/bin/env python3
"""
Memory Monitor - Simple memory usage tracking utility

This module provides basic memory monitoring capabilities to help
identify memory leaks and track resource usage in the audio system.
"""

import os
import psutil
import logging
import threading
import time
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class MemoryMonitor:
    """
    Simple memory monitoring utility for tracking memory usage.
    
    Features:
    - Periodic memory usage logging
    - Memory usage statistics
    - Process memory tracking
    - Simple leak detection
    """
    
    def __init__(self, log_interval: float = 60.0, enable_logging: bool = True):
        """
        Initialize memory monitor.
        
        Args:
            log_interval: Interval in seconds between memory logs
            enable_logging: Whether to enable periodic logging
        """
        self.log_interval = log_interval
        self.enable_logging = enable_logging
        
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._process = psutil.Process()
        
        # Memory statistics
        self._initial_memory = None
        self._peak_memory = 0
        self._memory_samples = []
        self._start_time = time.time()
        
    def start(self) -> None:
        """Start memory monitoring."""
        if self._monitor_thread is not None:
            logger.warning("Memory monitor already started")
            return
            
        self._initial_memory = self._get_memory_usage()
        self._peak_memory = self._initial_memory
        
        if self.enable_logging:
            self._stop_event.clear()
            self._monitor_thread = threading.Thread(
                target=self._monitor_loop,
                name="MemoryMonitor",
                daemon=True
            )
            self._monitor_thread.start()
            logger.info(f"Memory monitor started - Initial memory: {self._initial_memory:.1f} MB")
        
    def stop(self) -> None:
        """Stop memory monitoring."""
        if self._monitor_thread is None:
            return
            
        self._stop_event.set()
        if self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=2.0)
        self._monitor_thread = None
        
        # Log final statistics
        self._log_final_stats()
        logger.info("Memory monitor stopped")
        
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get current memory statistics."""
        current_memory = self._get_memory_usage()
        uptime = time.time() - self._start_time
        
        return {
            'current_mb': current_memory,
            'initial_mb': self._initial_memory or 0,
            'peak_mb': self._peak_memory,
            'growth_mb': current_memory - (self._initial_memory or 0),
            'uptime_seconds': uptime,
            'samples_count': len(self._memory_samples)
        }
        
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            memory_info = self._process.memory_info()
            return memory_info.rss / 1024 / 1024  # Convert to MB
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return 0.0
            
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        logger.debug("Memory monitor loop started")
        
        while not self._stop_event.is_set():
            try:
                current_memory = self._get_memory_usage()
                
                # Update statistics
                self._memory_samples.append(current_memory)
                if current_memory > self._peak_memory:
                    self._peak_memory = current_memory
                
                # Keep only recent samples (last hour)
                max_samples = int(3600 / self.log_interval)
                if len(self._memory_samples) > max_samples:
                    self._memory_samples = self._memory_samples[-max_samples:]
                
                # Log memory usage
                if self._initial_memory:
                    growth = current_memory - self._initial_memory
                    uptime = time.time() - self._start_time
                    logger.info(f"Memory: {current_memory:.1f} MB (growth: {growth:+.1f} MB, peak: {self._peak_memory:.1f} MB, uptime: {uptime:.0f}s)")

                    # Force garbage collection every 10 minutes to help with memory cleanup
                    if int(uptime) % 600 == 0:  # Every 10 minutes
                        import gc
                        collected = gc.collect()
                        logger.info(f"Forced garbage collection: collected {collected} objects")
                else:
                    logger.info(f"Memory: {current_memory:.1f} MB")

                # Sleep until next check
                self._stop_event.wait(self.log_interval)
                
            except Exception as e:
                logger.error(f"Error in memory monitor loop: {e}")
                self._stop_event.wait(self.log_interval)
                
        logger.debug("Memory monitor loop stopped")
        
    def _log_final_stats(self) -> None:
        """Log final memory statistics."""
        if not self._initial_memory or not self._memory_samples:
            return
            
        current_memory = self._memory_samples[-1]
        growth = current_memory - self._initial_memory
        uptime = time.time() - self._start_time
        
        # Calculate average growth rate
        if len(self._memory_samples) > 1 and uptime > 0:
            growth_rate = growth / (uptime / 3600)  # MB per hour
            logger.info(f"Memory Monitor Final Stats:")
            logger.info(f"  Initial: {self._initial_memory:.1f} MB")
            logger.info(f"  Final: {current_memory:.1f} MB")
            logger.info(f"  Peak: {self._peak_memory:.1f} MB")
            logger.info(f"  Growth: {growth:+.1f} MB ({growth_rate:+.2f} MB/hour)")
            logger.info(f"  Uptime: {uptime:.0f} seconds")
            logger.info(f"  Samples: {len(self._memory_samples)}")


# Global memory monitor instance
_memory_monitor: Optional[MemoryMonitor] = None


def start_memory_monitoring(log_interval: float = 300.0) -> None:
    """Start global memory monitoring."""
    global _memory_monitor
    
    if _memory_monitor is not None:
        logger.warning("Memory monitoring already started")
        return
        
    _memory_monitor = MemoryMonitor(log_interval=log_interval)
    _memory_monitor.start()


def stop_memory_monitoring() -> None:
    """Stop global memory monitoring."""
    global _memory_monitor
    
    if _memory_monitor is not None:
        _memory_monitor.stop()
        _memory_monitor = None


def get_memory_stats() -> Optional[Dict[str, Any]]:
    """Get current memory statistics from global monitor."""
    global _memory_monitor
    
    if _memory_monitor is not None:
        return _memory_monitor.get_memory_stats()
    return None
