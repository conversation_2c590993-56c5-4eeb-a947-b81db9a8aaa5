"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvdG9tc3V5cy9Eb2N1bWVudHMvR2l0SHViL1NheVdlYXRoZXJfcmlkZ2UvaGxzLXBsYXllci1hcHAvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9udW1iZXIvZGlzdC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9udW1iZXIvc3JjL251bWJlci50c1xuZnVuY3Rpb24gY2xhbXAodmFsdWUsIFttaW4sIG1heF0pIHtcbiAgcmV0dXJuIE1hdGgubWluKG1heCwgTWF0aC5tYXgobWluLCB2YWx1ZSkpO1xufVxuZXhwb3J0IHtcbiAgY2xhbXBcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvdG9tc3V5cy9Eb2N1bWVudHMvR2l0SHViL1NheVdlYXRoZXJfcmlkZ2UvaGxzLXBsYXllci1hcHAvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy90b21zdXlzL0RvY3VtZW50cy9HaXRIdWIvU2F5V2VhdGhlcl9yaWRnZS9obHMtcGxheWVyLWFwcC9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9kaXJlY3Rpb24vc3JjL2RpcmVjdGlvbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgRGlyZWN0aW9uQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQodm9pZCAwKTtcbnZhciBEaXJlY3Rpb25Qcm92aWRlciA9IChwcm9wcykgPT4ge1xuICBjb25zdCB7IGRpciwgY2hpbGRyZW4gfSA9IHByb3BzO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChEaXJlY3Rpb25Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBkaXIsIGNoaWxkcmVuIH0pO1xufTtcbmZ1bmN0aW9uIHVzZURpcmVjdGlvbihsb2NhbERpcikge1xuICBjb25zdCBnbG9iYWxEaXIgPSBSZWFjdC51c2VDb250ZXh0KERpcmVjdGlvbkNvbnRleHQpO1xuICByZXR1cm4gbG9jYWxEaXIgfHwgZ2xvYmFsRGlyIHx8IFwibHRyXCI7XG59XG52YXIgUHJvdmlkZXIgPSBEaXJlY3Rpb25Qcm92aWRlcjtcbmV4cG9ydCB7XG4gIERpcmVjdGlvblByb3ZpZGVyLFxuICBQcm92aWRlcixcbiAgdXNlRGlyZWN0aW9uXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                stylesRef.current = node2 ? getComputedStyle(node2) : null;\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByZXNlbmNlL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1QjtBQUNTO0FBQ0E7O0FDRlQ7QUFXaEIsU0FBUyxnQkFDZCxjQUNBLFNBQ0E7SUFDQSxPQUFhO3NDQUFXLENBQUMsT0FBd0I7WUFDL0MsTUFBTSxZQUFhLFFBQVEsS0FBSyxFQUFVLEtBQUs7WUFDL0MsT0FBTyxhQUFhO1FBQ3RCO3FDQUFHLFlBQVk7QUFDakI7O0FEVEEsSUFBTSxXQUFvQyxDQUFDO0lBQ3pDLE1BQU0sRUFBRSxTQUFTLFNBQVMsSUFBSTtJQUM5QixNQUFNLFdBQVcsWUFBWSxPQUFPO0lBRXBDLE1BQU0sUUFDSixPQUFPLGFBQWEsYUFDaEIsU0FBUztRQUFFLFNBQVMsU0FBUztJQUFVLENBQUMsSUFDbEMsNENBQVMsS0FBSyxRQUFRO0lBR2xDLE1BQU0sTUFBTSw2RUFBZSxDQUFDLFNBQVMsS0FBSyxjQUFjLEtBQUssQ0FBQztJQUM5RCxNQUFNLGFBQWEsT0FBTyxhQUFhO0lBQ3ZDLE9BQU8sY0FBYyxTQUFTLDBCQUFrQixnREFBYSxPQUFPO1FBQUU7SUFBSSxDQUFDLElBQUk7QUFDakY7QUFFQSxTQUFTLGNBQWM7QUFNdkIsU0FBUyxZQUFZLFNBQWtCO0lBQ3JDLE1BQU0sQ0FBQyxNQUFNLE9BQU8sSUFBVSw0Q0FBc0I7SUFDcEQsTUFBTSxZQUFrQiwwQ0FBbUMsSUFBSTtJQUMvRCxNQUFNLGlCQUF1QiwwQ0FBTyxPQUFPO0lBQzNDLE1BQU0sdUJBQTZCLDBDQUFlLE1BQU07SUFDeEQsTUFBTSxlQUFlLFVBQVUsWUFBWTtJQUMzQyxNQUFNLENBQUMsT0FBTyxJQUFJLElBQUksZ0JBQWdCLGNBQWM7UUFDbEQsU0FBUztZQUNQLFNBQVM7WUFDVCxlQUFlO1FBQ2pCO1FBQ0Esa0JBQWtCO1lBQ2hCLE9BQU87WUFDUCxlQUFlO1FBQ2pCO1FBQ0EsV0FBVztZQUNULE9BQU87UUFDVDtJQUNGLENBQUM7SUFFSztpQ0FBVTtZQUNkLE1BQU0sdUJBQXVCLGlCQUFpQixVQUFVLE9BQU87WUFDL0QscUJBQXFCLFVBQVUsVUFBVSxZQUFZLHVCQUF1QjtRQUM5RTtnQ0FBRztRQUFDLEtBQUs7S0FBQztJQUVWLGtGQUFlO3VDQUFDO1lBQ2QsTUFBTSxTQUFTLFVBQVU7WUFDekIsTUFBTSxhQUFhLGVBQWU7WUFDbEMsTUFBTSxvQkFBb0IsZUFBZTtZQUV6QyxJQUFJLG1CQUFtQjtnQkFDckIsTUFBTSxvQkFBb0IscUJBQXFCO2dCQUMvQyxNQUFNLHVCQUF1QixpQkFBaUIsTUFBTTtnQkFFcEQsSUFBSSxTQUFTO29CQUNYLEtBQUssT0FBTztnQkFDZCxXQUFXLHlCQUF5QixVQUFVLFFBQVEsWUFBWSxRQUFRO29CQUd4RSxLQUFLLFNBQVM7Z0JBQ2hCLE9BQU87b0JBT0wsTUFBTSxjQUFjLHNCQUFzQjtvQkFFMUMsSUFBSSxjQUFjLGFBQWE7d0JBQzdCLEtBQUssZUFBZTtvQkFDdEIsT0FBTzt3QkFDTCxLQUFLLFNBQVM7b0JBQ2hCO2dCQUNGO2dCQUVBLGVBQWUsVUFBVTtZQUMzQjtRQUNGO3NDQUFHO1FBQUM7UUFBUyxJQUFJO0tBQUM7SUFFbEIsa0ZBQWU7dUNBQUM7WUFDZCxJQUFJLE1BQU07Z0JBQ1IsSUFBSTtnQkFDSixNQUFNLGNBQWMsS0FBSyxjQUFjLGVBQWU7Z0JBTXRELE1BQU07c0VBQXFCLENBQUM7d0JBQzFCLE1BQU0sdUJBQXVCLGlCQUFpQixVQUFVLE9BQU87d0JBQy9ELE1BQU0scUJBQXFCLHFCQUFxQixTQUFTLE1BQU0sYUFBYTt3QkFDNUUsSUFBSSxNQUFNLFdBQVcsUUFBUSxvQkFBb0I7NEJBVy9DLEtBQUssZUFBZTs0QkFDcEIsSUFBSSxDQUFDLGVBQWUsU0FBUztnQ0FDM0IsTUFBTSxrQkFBa0IsS0FBSyxNQUFNO2dDQUNuQyxLQUFLLE1BQU0sb0JBQW9CO2dDQUsvQixZQUFZLFlBQVk7c0ZBQVc7d0NBQ2pDLElBQUksS0FBSyxNQUFNLHNCQUFzQixZQUFZOzRDQUMvQyxLQUFLLE1BQU0sb0JBQW9CO3dDQUNqQztvQ0FDRixDQUFDOzs0QkFDSDt3QkFDRjtvQkFDRjs7Z0JBQ0EsTUFBTTt3RUFBdUIsQ0FBQzt3QkFDNUIsSUFBSSxNQUFNLFdBQVcsTUFBTTs0QkFFekIscUJBQXFCLFVBQVUsaUJBQWlCLFVBQVUsT0FBTzt3QkFDbkU7b0JBQ0Y7O2dCQUNBLEtBQUssaUJBQWlCLGtCQUFrQixvQkFBb0I7Z0JBQzVELEtBQUssaUJBQWlCLG1CQUFtQixrQkFBa0I7Z0JBQzNELEtBQUssaUJBQWlCLGdCQUFnQixrQkFBa0I7Z0JBQ3hEO21EQUFPO3dCQUNMLFlBQVksYUFBYSxTQUFTO3dCQUNsQyxLQUFLLG9CQUFvQixrQkFBa0Isb0JBQW9CO3dCQUMvRCxLQUFLLG9CQUFvQixtQkFBbUIsa0JBQWtCO3dCQUM5RCxLQUFLLG9CQUFvQixnQkFBZ0Isa0JBQWtCO29CQUM3RDs7WUFDRixPQUFPO2dCQUdMLEtBQUssZUFBZTtZQUN0QjtRQUNGO3NDQUFHO1FBQUM7UUFBTSxJQUFJO0tBQUM7SUFFZixPQUFPO1FBQ0wsV0FBVztZQUFDO1lBQVcsa0JBQWtCO1NBQUEsQ0FBRSxTQUFTLEtBQUs7UUFDekQsS0FBVzt1Q0FBWSxDQUFDQztnQkFDdEIsVUFBVSxVQUFVQSxRQUFPLGlCQUFpQkEsS0FBSSxJQUFJO2dCQUNwRCxRQUFRQSxLQUFJO1lBQ2Q7c0NBQUcsQ0FBQyxDQUFDO0lBQ1A7QUFDRjtBQUlBLFNBQVMsaUJBQWlCLFFBQW9DO0lBQzVELE9BQU8sUUFBUSxpQkFBaUI7QUFDbEM7QUFPQSxTQUFTLGNBQWMsU0FBMkQ7SUFFaEYsSUFBSSxTQUFTLE9BQU8seUJBQXlCLFFBQVEsT0FBTyxLQUFLLEdBQUc7SUFDcEUsSUFBSSxVQUFVLFVBQVUsb0JBQW9CLFVBQVUsT0FBTztJQUM3RCxJQUFJLFNBQVM7UUFDWCxPQUFRLFFBQWdCO0lBQzFCO0lBR0EsU0FBUyxPQUFPLHlCQUF5QixTQUFTLEtBQUssR0FBRztJQUMxRCxVQUFVLFVBQVUsb0JBQW9CLFVBQVUsT0FBTztJQUN6RCxJQUFJLFNBQVM7UUFDWCxPQUFPLFFBQVEsTUFBTTtJQUN2QjtJQUdBLE9BQU8sUUFBUSxNQUFNLE9BQVEsUUFBZ0I7QUFDL0M7QUFFQSxJQUFNLE9BQU8iLCJzb3VyY2VzIjpbIi9Vc2Vycy90b21zdXlzL0RvY3VtZW50cy9HaXRIdWIvU2F5V2VhdGhlcl9yaWRnZS9zcmMvcHJlc2VuY2UudHN4IiwiL1VzZXJzL3RvbXN1eXMvRG9jdW1lbnRzL0dpdEh1Yi9TYXlXZWF0aGVyX3JpZGdlL3NyYy91c2Utc3RhdGUtbWFjaGluZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlQ29tcG9zZWRSZWZzIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcyc7XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QnO1xuaW1wb3J0IHsgdXNlU3RhdGVNYWNoaW5lIH0gZnJvbSAnLi91c2Utc3RhdGUtbWFjaGluZSc7XG5cbmludGVyZmFjZSBQcmVzZW5jZVByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0RWxlbWVudCB8ICgocHJvcHM6IHsgcHJlc2VudDogYm9vbGVhbiB9KSA9PiBSZWFjdC5SZWFjdEVsZW1lbnQpO1xuICBwcmVzZW50OiBib29sZWFuO1xufVxuXG5jb25zdCBQcmVzZW5jZTogUmVhY3QuRkM8UHJlc2VuY2VQcm9wcz4gPSAocHJvcHMpID0+IHtcbiAgY29uc3QgeyBwcmVzZW50LCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIGNvbnN0IHByZXNlbmNlID0gdXNlUHJlc2VuY2UocHJlc2VudCk7XG5cbiAgY29uc3QgY2hpbGQgPSAoXG4gICAgdHlwZW9mIGNoaWxkcmVuID09PSAnZnVuY3Rpb24nXG4gICAgICA/IGNoaWxkcmVuKHsgcHJlc2VudDogcHJlc2VuY2UuaXNQcmVzZW50IH0pXG4gICAgICA6IFJlYWN0LkNoaWxkcmVuLm9ubHkoY2hpbGRyZW4pXG4gICkgYXMgUmVhY3QuUmVhY3RFbGVtZW50PHsgcmVmPzogUmVhY3QuUmVmPEhUTUxFbGVtZW50PiB9PjtcblxuICBjb25zdCByZWYgPSB1c2VDb21wb3NlZFJlZnMocHJlc2VuY2UucmVmLCBnZXRFbGVtZW50UmVmKGNoaWxkKSk7XG4gIGNvbnN0IGZvcmNlTW91bnQgPSB0eXBlb2YgY2hpbGRyZW4gPT09ICdmdW5jdGlvbic7XG4gIHJldHVybiBmb3JjZU1vdW50IHx8IHByZXNlbmNlLmlzUHJlc2VudCA/IFJlYWN0LmNsb25lRWxlbWVudChjaGlsZCwgeyByZWYgfSkgOiBudWxsO1xufTtcblxuUHJlc2VuY2UuZGlzcGxheU5hbWUgPSAnUHJlc2VuY2UnO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiB1c2VQcmVzZW5jZVxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5mdW5jdGlvbiB1c2VQcmVzZW5jZShwcmVzZW50OiBib29sZWFuKSB7XG4gIGNvbnN0IFtub2RlLCBzZXROb2RlXSA9IFJlYWN0LnVzZVN0YXRlPEhUTUxFbGVtZW50PigpO1xuICBjb25zdCBzdHlsZXNSZWYgPSBSZWFjdC51c2VSZWY8Q1NTU3R5bGVEZWNsYXJhdGlvbiB8IG51bGw+KG51bGwpO1xuICBjb25zdCBwcmV2UHJlc2VudFJlZiA9IFJlYWN0LnVzZVJlZihwcmVzZW50KTtcbiAgY29uc3QgcHJldkFuaW1hdGlvbk5hbWVSZWYgPSBSZWFjdC51c2VSZWY8c3RyaW5nPignbm9uZScpO1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBwcmVzZW50ID8gJ21vdW50ZWQnIDogJ3VubW91bnRlZCc7XG4gIGNvbnN0IFtzdGF0ZSwgc2VuZF0gPSB1c2VTdGF0ZU1hY2hpbmUoaW5pdGlhbFN0YXRlLCB7XG4gICAgbW91bnRlZDoge1xuICAgICAgVU5NT1VOVDogJ3VubW91bnRlZCcsXG4gICAgICBBTklNQVRJT05fT1VUOiAndW5tb3VudFN1c3BlbmRlZCcsXG4gICAgfSxcbiAgICB1bm1vdW50U3VzcGVuZGVkOiB7XG4gICAgICBNT1VOVDogJ21vdW50ZWQnLFxuICAgICAgQU5JTUFUSU9OX0VORDogJ3VubW91bnRlZCcsXG4gICAgfSxcbiAgICB1bm1vdW50ZWQ6IHtcbiAgICAgIE1PVU5UOiAnbW91bnRlZCcsXG4gICAgfSxcbiAgfSk7XG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjdXJyZW50QW5pbWF0aW9uTmFtZSA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgIHByZXZBbmltYXRpb25OYW1lUmVmLmN1cnJlbnQgPSBzdGF0ZSA9PT0gJ21vdW50ZWQnID8gY3VycmVudEFuaW1hdGlvbk5hbWUgOiAnbm9uZSc7XG4gIH0sIFtzdGF0ZV0pO1xuXG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc3R5bGVzID0gc3R5bGVzUmVmLmN1cnJlbnQ7XG4gICAgY29uc3Qgd2FzUHJlc2VudCA9IHByZXZQcmVzZW50UmVmLmN1cnJlbnQ7XG4gICAgY29uc3QgaGFzUHJlc2VudENoYW5nZWQgPSB3YXNQcmVzZW50ICE9PSBwcmVzZW50O1xuXG4gICAgaWYgKGhhc1ByZXNlbnRDaGFuZ2VkKSB7XG4gICAgICBjb25zdCBwcmV2QW5pbWF0aW9uTmFtZSA9IHByZXZBbmltYXRpb25OYW1lUmVmLmN1cnJlbnQ7XG4gICAgICBjb25zdCBjdXJyZW50QW5pbWF0aW9uTmFtZSA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzKTtcblxuICAgICAgaWYgKHByZXNlbnQpIHtcbiAgICAgICAgc2VuZCgnTU9VTlQnKTtcbiAgICAgIH0gZWxzZSBpZiAoY3VycmVudEFuaW1hdGlvbk5hbWUgPT09ICdub25lJyB8fCBzdHlsZXM/LmRpc3BsYXkgPT09ICdub25lJykge1xuICAgICAgICAvLyBJZiB0aGVyZSBpcyBubyBleGl0IGFuaW1hdGlvbiBvciB0aGUgZWxlbWVudCBpcyBoaWRkZW4sIGFuaW1hdGlvbnMgd29uJ3QgcnVuXG4gICAgICAgIC8vIHNvIHdlIHVubW91bnQgaW5zdGFudGx5XG4gICAgICAgIHNlbmQoJ1VOTU9VTlQnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBXaGVuIGBwcmVzZW50YCBjaGFuZ2VzIHRvIGBmYWxzZWAsIHdlIGNoZWNrIGNoYW5nZXMgdG8gYW5pbWF0aW9uLW5hbWUgdG9cbiAgICAgICAgICogZGV0ZXJtaW5lIHdoZXRoZXIgYW4gYW5pbWF0aW9uIGhhcyBzdGFydGVkLiBXZSBjaG9zZSB0aGlzIGFwcHJvYWNoIChyZWFkaW5nXG4gICAgICAgICAqIGNvbXB1dGVkIHN0eWxlcykgYmVjYXVzZSB0aGVyZSBpcyBubyBgYW5pbWF0aW9ucnVuYCBldmVudCBhbmQgYGFuaW1hdGlvbnN0YXJ0YFxuICAgICAgICAgKiBmaXJlcyBhZnRlciBgYW5pbWF0aW9uLWRlbGF5YCBoYXMgZXhwaXJlZCB3aGljaCB3b3VsZCBiZSB0b28gbGF0ZS5cbiAgICAgICAgICovXG4gICAgICAgIGNvbnN0IGlzQW5pbWF0aW5nID0gcHJldkFuaW1hdGlvbk5hbWUgIT09IGN1cnJlbnRBbmltYXRpb25OYW1lO1xuXG4gICAgICAgIGlmICh3YXNQcmVzZW50ICYmIGlzQW5pbWF0aW5nKSB7XG4gICAgICAgICAgc2VuZCgnQU5JTUFUSU9OX09VVCcpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNlbmQoJ1VOTU9VTlQnKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBwcmV2UHJlc2VudFJlZi5jdXJyZW50ID0gcHJlc2VudDtcbiAgICB9XG4gIH0sIFtwcmVzZW50LCBzZW5kXSk7XG5cbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAobm9kZSkge1xuICAgICAgbGV0IHRpbWVvdXRJZDogbnVtYmVyO1xuICAgICAgY29uc3Qgb3duZXJXaW5kb3cgPSBub2RlLm93bmVyRG9jdW1lbnQuZGVmYXVsdFZpZXcgPz8gd2luZG93O1xuICAgICAgLyoqXG4gICAgICAgKiBUcmlnZ2VyaW5nIGFuIEFOSU1BVElPTl9PVVQgZHVyaW5nIGFuIEFOSU1BVElPTl9JTiB3aWxsIGZpcmUgYW4gYGFuaW1hdGlvbmNhbmNlbGBcbiAgICAgICAqIGV2ZW50IGZvciBBTklNQVRJT05fSU4gYWZ0ZXIgd2UgaGF2ZSBlbnRlcmVkIGB1bm1vdW50U3VzcGVuZGVkYCBzdGF0ZS4gU28sIHdlXG4gICAgICAgKiBtYWtlIHN1cmUgd2Ugb25seSB0cmlnZ2VyIEFOSU1BVElPTl9FTkQgZm9yIHRoZSBjdXJyZW50bHkgYWN0aXZlIGFuaW1hdGlvbi5cbiAgICAgICAqL1xuICAgICAgY29uc3QgaGFuZGxlQW5pbWF0aW9uRW5kID0gKGV2ZW50OiBBbmltYXRpb25FdmVudCkgPT4ge1xuICAgICAgICBjb25zdCBjdXJyZW50QW5pbWF0aW9uTmFtZSA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgICAgICBjb25zdCBpc0N1cnJlbnRBbmltYXRpb24gPSBjdXJyZW50QW5pbWF0aW9uTmFtZS5pbmNsdWRlcyhldmVudC5hbmltYXRpb25OYW1lKTtcbiAgICAgICAgaWYgKGV2ZW50LnRhcmdldCA9PT0gbm9kZSAmJiBpc0N1cnJlbnRBbmltYXRpb24pIHtcbiAgICAgICAgICAvLyBXaXRoIFJlYWN0IDE4IGNvbmN1cnJlbmN5IHRoaXMgdXBkYXRlIGlzIGFwcGxpZWQgYSBmcmFtZSBhZnRlciB0aGVcbiAgICAgICAgICAvLyBhbmltYXRpb24gZW5kcywgY3JlYXRpbmcgYSBmbGFzaCBvZiB2aXNpYmxlIGNvbnRlbnQuIEJ5IHNldHRpbmcgdGhlXG4gICAgICAgICAgLy8gYW5pbWF0aW9uIGZpbGwgbW9kZSB0byBcImZvcndhcmRzXCIsIHdlIGZvcmNlIHRoZSBub2RlIHRvIGtlZXAgdGhlXG4gICAgICAgICAgLy8gc3R5bGVzIG9mIHRoZSBsYXN0IGtleWZyYW1lLCByZW1vdmluZyB0aGUgZmxhc2guXG4gICAgICAgICAgLy9cbiAgICAgICAgICAvLyBQcmV2aW91c2x5IHdlIGZsdXNoZWQgdGhlIHVwZGF0ZSB2aWEgUmVhY3REb20uZmx1c2hTeW5jLCBidXQgd2l0aFxuICAgICAgICAgIC8vIGV4aXQgYW5pbWF0aW9ucyB0aGlzIHJlc3VsdGVkIGluIHRoZSBub2RlIGJlaW5nIHJlbW92ZWQgZnJvbSB0aGVcbiAgICAgICAgICAvLyBET00gYmVmb3JlIHRoZSBzeW50aGV0aWMgYW5pbWF0aW9uRW5kIGV2ZW50IHdhcyBkaXNwYXRjaGVkLCBtZWFuaW5nXG4gICAgICAgICAgLy8gdXNlci1wcm92aWRlZCBldmVudCBoYW5kbGVycyB3b3VsZCBub3QgYmUgY2FsbGVkLlxuICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9yYWRpeC11aS9wcmltaXRpdmVzL3B1bGwvMTg0OVxuICAgICAgICAgIHNlbmQoJ0FOSU1BVElPTl9FTkQnKTtcbiAgICAgICAgICBpZiAoIXByZXZQcmVzZW50UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRGaWxsTW9kZSA9IG5vZGUuc3R5bGUuYW5pbWF0aW9uRmlsbE1vZGU7XG4gICAgICAgICAgICBub2RlLnN0eWxlLmFuaW1hdGlvbkZpbGxNb2RlID0gJ2ZvcndhcmRzJztcbiAgICAgICAgICAgIC8vIFJlc2V0IHRoZSBzdHlsZSBhZnRlciB0aGUgbm9kZSBoYWQgdGltZSB0byB1bm1vdW50IChmb3IgY2FzZXNcbiAgICAgICAgICAgIC8vIHdoZXJlIHRoZSBjb21wb25lbnQgY2hvb3NlcyBub3QgdG8gdW5tb3VudCkuIERvaW5nIHRoaXMgYW55XG4gICAgICAgICAgICAvLyBzb29uZXIgdGhhbiBgc2V0VGltZW91dGAgKGUuZy4gd2l0aCBgcmVxdWVzdEFuaW1hdGlvbkZyYW1lYClcbiAgICAgICAgICAgIC8vIHN0aWxsIGNhdXNlcyBhIGZsYXNoLlxuICAgICAgICAgICAgdGltZW91dElkID0gb3duZXJXaW5kb3cuc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIGlmIChub2RlLnN0eWxlLmFuaW1hdGlvbkZpbGxNb2RlID09PSAnZm9yd2FyZHMnKSB7XG4gICAgICAgICAgICAgICAgbm9kZS5zdHlsZS5hbmltYXRpb25GaWxsTW9kZSA9IGN1cnJlbnRGaWxsTW9kZTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgY29uc3QgaGFuZGxlQW5pbWF0aW9uU3RhcnQgPSAoZXZlbnQ6IEFuaW1hdGlvbkV2ZW50KSA9PiB7XG4gICAgICAgIGlmIChldmVudC50YXJnZXQgPT09IG5vZGUpIHtcbiAgICAgICAgICAvLyBpZiBhbmltYXRpb24gb2NjdXJyZWQsIHN0b3JlIGl0cyBuYW1lIGFzIHRoZSBwcmV2aW91cyBhbmltYXRpb24uXG4gICAgICAgICAgcHJldkFuaW1hdGlvbk5hbWVSZWYuY3VycmVudCA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgbm9kZS5hZGRFdmVudExpc3RlbmVyKCdhbmltYXRpb25zdGFydCcsIGhhbmRsZUFuaW1hdGlvblN0YXJ0KTtcbiAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uY2FuY2VsJywgaGFuZGxlQW5pbWF0aW9uRW5kKTtcbiAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uZW5kJywgaGFuZGxlQW5pbWF0aW9uRW5kKTtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIG93bmVyV2luZG93LmNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2FuaW1hdGlvbnN0YXJ0JywgaGFuZGxlQW5pbWF0aW9uU3RhcnQpO1xuICAgICAgICBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2FuaW1hdGlvbmNhbmNlbCcsIGhhbmRsZUFuaW1hdGlvbkVuZCk7XG4gICAgICAgIG5vZGUucmVtb3ZlRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uZW5kJywgaGFuZGxlQW5pbWF0aW9uRW5kKTtcbiAgICAgIH07XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFRyYW5zaXRpb24gdG8gdGhlIHVubW91bnRlZCBzdGF0ZSBpZiB0aGUgbm9kZSBpcyByZW1vdmVkIHByZW1hdHVyZWx5LlxuICAgICAgLy8gV2UgYXZvaWQgZG9pbmcgc28gZHVyaW5nIGNsZWFudXAgYXMgdGhlIG5vZGUgbWF5IGNoYW5nZSBidXQgc3RpbGwgZXhpc3QuXG4gICAgICBzZW5kKCdBTklNQVRJT05fRU5EJyk7XG4gICAgfVxuICB9LCBbbm9kZSwgc2VuZF0pO1xuXG4gIHJldHVybiB7XG4gICAgaXNQcmVzZW50OiBbJ21vdW50ZWQnLCAndW5tb3VudFN1c3BlbmRlZCddLmluY2x1ZGVzKHN0YXRlKSxcbiAgICByZWY6IFJlYWN0LnVzZUNhbGxiYWNrKChub2RlOiBIVE1MRWxlbWVudCkgPT4ge1xuICAgICAgc3R5bGVzUmVmLmN1cnJlbnQgPSBub2RlID8gZ2V0Q29tcHV0ZWRTdHlsZShub2RlKSA6IG51bGw7XG4gICAgICBzZXROb2RlKG5vZGUpO1xuICAgIH0sIFtdKSxcbiAgfTtcbn1cblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5mdW5jdGlvbiBnZXRBbmltYXRpb25OYW1lKHN0eWxlczogQ1NTU3R5bGVEZWNsYXJhdGlvbiB8IG51bGwpIHtcbiAgcmV0dXJuIHN0eWxlcz8uYW5pbWF0aW9uTmFtZSB8fCAnbm9uZSc7XG59XG5cbi8vIEJlZm9yZSBSZWFjdCAxOSBhY2Nlc3NpbmcgYGVsZW1lbnQucHJvcHMucmVmYCB3aWxsIHRocm93IGEgd2FybmluZyBhbmQgc3VnZ2VzdCB1c2luZyBgZWxlbWVudC5yZWZgXG4vLyBBZnRlciBSZWFjdCAxOSBhY2Nlc3NpbmcgYGVsZW1lbnQucmVmYCBkb2VzIHRoZSBvcHBvc2l0ZS5cbi8vIGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC9wdWxsLzI4MzQ4XG4vL1xuLy8gQWNjZXNzIHRoZSByZWYgdXNpbmcgdGhlIG1ldGhvZCB0aGF0IGRvZXNuJ3QgeWllbGQgYSB3YXJuaW5nLlxuZnVuY3Rpb24gZ2V0RWxlbWVudFJlZihlbGVtZW50OiBSZWFjdC5SZWFjdEVsZW1lbnQ8eyByZWY/OiBSZWFjdC5SZWY8dW5rbm93bj4gfT4pIHtcbiAgLy8gUmVhY3QgPD0xOCBpbiBERVZcbiAgbGV0IGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoZWxlbWVudC5wcm9wcywgJ3JlZicpPy5nZXQ7XG4gIGxldCBtYXlXYXJuID0gZ2V0dGVyICYmICdpc1JlYWN0V2FybmluZycgaW4gZ2V0dGVyICYmIGdldHRlci5pc1JlYWN0V2FybmluZztcbiAgaWYgKG1heVdhcm4pIHtcbiAgICByZXR1cm4gKGVsZW1lbnQgYXMgYW55KS5yZWY7XG4gIH1cblxuICAvLyBSZWFjdCAxOSBpbiBERVZcbiAgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlbGVtZW50LCAncmVmJyk/LmdldDtcbiAgbWF5V2FybiA9IGdldHRlciAmJiAnaXNSZWFjdFdhcm5pbmcnIGluIGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmc7XG4gIGlmIChtYXlXYXJuKSB7XG4gICAgcmV0dXJuIGVsZW1lbnQucHJvcHMucmVmO1xuICB9XG5cbiAgLy8gTm90IERFVlxuICByZXR1cm4gZWxlbWVudC5wcm9wcy5yZWYgfHwgKGVsZW1lbnQgYXMgYW55KS5yZWY7XG59XG5cbmNvbnN0IFJvb3QgPSBQcmVzZW5jZTtcblxuZXhwb3J0IHtcbiAgUHJlc2VuY2UsXG4gIC8vXG4gIFJvb3QsXG59O1xuZXhwb3J0IHR5cGUgeyBQcmVzZW5jZVByb3BzIH07XG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbnR5cGUgTWFjaGluZTxTPiA9IHsgW2s6IHN0cmluZ106IHsgW2s6IHN0cmluZ106IFMgfSB9O1xudHlwZSBNYWNoaW5lU3RhdGU8VD4gPSBrZXlvZiBUO1xudHlwZSBNYWNoaW5lRXZlbnQ8VD4gPSBrZXlvZiBVbmlvblRvSW50ZXJzZWN0aW9uPFRba2V5b2YgVF0+O1xuXG4vLyDwn6SvIGh0dHBzOi8vZmV0dGJsb2cuZXUvdHlwZXNjcmlwdC11bmlvbi10by1pbnRlcnNlY3Rpb24vXG50eXBlIFVuaW9uVG9JbnRlcnNlY3Rpb248VD4gPSAoVCBleHRlbmRzIGFueSA/ICh4OiBUKSA9PiBhbnkgOiBuZXZlcikgZXh0ZW5kcyAoeDogaW5mZXIgUikgPT4gYW55XG4gID8gUlxuICA6IG5ldmVyO1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlU3RhdGVNYWNoaW5lPE0+KFxuICBpbml0aWFsU3RhdGU6IE1hY2hpbmVTdGF0ZTxNPixcbiAgbWFjaGluZTogTSAmIE1hY2hpbmU8TWFjaGluZVN0YXRlPE0+PlxuKSB7XG4gIHJldHVybiBSZWFjdC51c2VSZWR1Y2VyKChzdGF0ZTogTWFjaGluZVN0YXRlPE0+LCBldmVudDogTWFjaGluZUV2ZW50PE0+KTogTWFjaGluZVN0YXRlPE0+ID0+IHtcbiAgICBjb25zdCBuZXh0U3RhdGUgPSAobWFjaGluZVtzdGF0ZV0gYXMgYW55KVtldmVudF07XG4gICAgcmV0dXJuIG5leHRTdGF0ZSA/PyBzdGF0ZTtcbiAgfSwgaW5pdGlhbFN0YXRlKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIm5vZGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDTztBQUNZO0FBQ1Y7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZ0VBQVUsY0FBYyxLQUFLO0FBQzVDLGVBQWUsNkNBQWdCO0FBQy9CLFlBQVksNkJBQTZCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFHLFNBQVMsc0NBQXNDO0FBQzdFLEdBQUc7QUFDSCxrQ0FBa0MsS0FBSztBQUN2QyxXQUFXO0FBQ1gsQ0FBQyxJQUFJO0FBQ0w7QUFDQSxjQUFjLGdEQUFrQjtBQUNoQztBQUNBO0FBS0U7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3RvbXN1eXMvRG9jdW1lbnRzL0dpdEh1Yi9TYXlXZWF0aGVyX3JpZGdlL2hscy1wbGF5ZXItYXBwL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9wcmltaXRpdmUudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCAqIGFzIFJlYWN0RE9NIGZyb20gXCJyZWFjdC1kb21cIjtcbmltcG9ydCB7IGNyZWF0ZVNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIE5PREVTID0gW1xuICBcImFcIixcbiAgXCJidXR0b25cIixcbiAgXCJkaXZcIixcbiAgXCJmb3JtXCIsXG4gIFwiaDJcIixcbiAgXCJoM1wiLFxuICBcImltZ1wiLFxuICBcImlucHV0XCIsXG4gIFwibGFiZWxcIixcbiAgXCJsaVwiLFxuICBcIm5hdlwiLFxuICBcIm9sXCIsXG4gIFwicFwiLFxuICBcInNlbGVjdFwiLFxuICBcInNwYW5cIixcbiAgXCJzdmdcIixcbiAgXCJ1bFwiXG5dO1xudmFyIFByaW1pdGl2ZSA9IE5PREVTLnJlZHVjZSgocHJpbWl0aXZlLCBub2RlKSA9PiB7XG4gIGNvbnN0IFNsb3QgPSBjcmVhdGVTbG90KGBQcmltaXRpdmUuJHtub2RlfWApO1xuICBjb25zdCBOb2RlID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgYXNDaGlsZCwgLi4ucHJpbWl0aXZlUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IG5vZGU7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgIHdpbmRvd1tTeW1ib2wuZm9yKFwicmFkaXgtdWlcIildID0gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goQ29tcCwgeyAuLi5wcmltaXRpdmVQcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYgfSk7XG4gIH0pO1xuICBOb2RlLmRpc3BsYXlOYW1lID0gYFByaW1pdGl2ZS4ke25vZGV9YDtcbiAgcmV0dXJuIHsgLi4ucHJpbWl0aXZlLCBbbm9kZV06IE5vZGUgfTtcbn0sIHt9KTtcbmZ1bmN0aW9uIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudCh0YXJnZXQsIGV2ZW50KSB7XG4gIGlmICh0YXJnZXQpIFJlYWN0RE9NLmZsdXNoU3luYygoKSA9PiB0YXJnZXQuZGlzcGF0Y2hFdmVudChldmVudCkpO1xufVxudmFyIFJvb3QgPSBQcmltaXRpdmU7XG5leHBvcnQge1xuICBQcmltaXRpdmUsXG4gIFJvb3QsXG4gIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // src/scroll-area.tsx\n\n\n\n\n\n\n\n\n\n\n// src/use-state-machine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/scroll-area.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollArea.useComposedRefs[composedRefs]\": (node)=>setScrollArea(node)\n    }[\"ScrollArea.useComposedRefs[composedRefs]\"]);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbar.useEffect\": ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n            return ({\n                \"ScrollAreaScrollbar.useEffect\": ()=>{\n                    isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n                }\n            })[\"ScrollAreaScrollbar.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbar.useEffect\"], [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n            const scrollArea = context.scrollArea;\n            let hideTimer = 0;\n            if (scrollArea) {\n                const handlePointerEnter = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        setVisible(true);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\"];\n                const handlePointerLeave = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>{\n                        hideTimer = window.setTimeout({\n                            \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>setVisible(false)\n                        }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"], context.scrollHideDelay);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"];\n                scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n                return ({\n                    \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n                    }\n                })[\"ScrollAreaScrollbarHover.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarHover.useEffect\"], [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\": ()=>send(\"SCROLL_END\")\n    }[\"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\"], 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            if (state === \"idle\") {\n                const hideTimer = window.setTimeout({\n                    \"ScrollAreaScrollbarScroll.useEffect.hideTimer\": ()=>send(\"HIDE\")\n                }[\"ScrollAreaScrollbarScroll.useEffect.hideTimer\"], context.scrollHideDelay);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>window.clearTimeout(hideTimer)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            const viewport = context.viewport;\n            const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n            if (viewport) {\n                let prevScrollPos = viewport[scrollDirection];\n                const handleScroll = {\n                    \"ScrollAreaScrollbarScroll.useEffect.handleScroll\": ()=>{\n                        const scrollPos = viewport[scrollDirection];\n                        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                        if (hasScrollInDirectionChanged) {\n                            send(\"SCROLL\");\n                            debounceScrollEnd();\n                        }\n                        prevScrollPos = scrollPos;\n                    }\n                }[\"ScrollAreaScrollbarScroll.useEffect.handleScroll\"];\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback({\n        \"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\": ()=>{\n            if (context.viewport) {\n                const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n                const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n                setVisible(isHorizontal ? isOverflowX : isOverflowY);\n            }\n        }\n    }[\"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\"], 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarX.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarX.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarY.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarY.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\": (node)=>setScrollbar(node)\n    }[\"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarImpl.useEffect\": ()=>{\n            const handleWheel = {\n                \"ScrollAreaScrollbarImpl.useEffect.handleWheel\": (event)=>{\n                    const element = event.target;\n                    const isScrollbarWheel = scrollbar?.contains(element);\n                    if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n                }\n            }[\"ScrollAreaScrollbarImpl.useEffect.handleWheel\"];\n            document.addEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n            return ({\n                \"ScrollAreaScrollbarImpl.useEffect\": ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                        passive: false\n                    })\n            })[\"ScrollAreaScrollbarImpl.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbarImpl.useEffect\"], [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaThumbImpl.useComposedRefs[composedRef]\": (node)=>scrollbarContext.onThumbChange(node)\n    }[\"ScrollAreaThumbImpl.useComposedRefs[composedRef]\"]);\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\": ()=>{\n            if (removeUnlinkedScrollListenerRef.current) {\n                removeUnlinkedScrollListenerRef.current();\n                removeUnlinkedScrollListenerRef.current = void 0;\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\"], 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaThumbImpl.useEffect\": ()=>{\n            const viewport = scrollAreaContext.viewport;\n            if (viewport) {\n                const handleScroll = {\n                    \"ScrollAreaThumbImpl.useEffect.handleScroll\": ()=>{\n                        debounceScrollEnd();\n                        if (!removeUnlinkedScrollListenerRef.current) {\n                            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                            removeUnlinkedScrollListenerRef.current = listener;\n                            onThumbPositionChange();\n                        }\n                    }\n                }[\"ScrollAreaThumbImpl.useEffect.handleScroll\"];\n                onThumbPositionChange();\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaThumbImpl.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaThumbImpl.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useEffect\"], [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const height2 = context.scrollbarX?.offsetHeight || 0;\n            context.onCornerHeightChange(height2);\n            setHeight(height2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    useResizeObserver(context.scrollbarY, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const width2 = context.scrollbarY?.offsetWidth || 0;\n            context.onCornerWidthChange(width2);\n            setWidth(width2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useDebounceCallback.useEffect\": ()=>({\n                \"useDebounceCallback.useEffect\": ()=>window.clearTimeout(debounceTimerRef.current)\n            })[\"useDebounceCallback.useEffect\"]\n    }[\"useDebounceCallback.useEffect\"], []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useDebounceCallback.useCallback\": ()=>{\n            window.clearTimeout(debounceTimerRef.current);\n            debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n        }\n    }[\"useDebounceCallback.useCallback\"], [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)({\n        \"useResizeObserver.useLayoutEffect\": ()=>{\n            let rAF = 0;\n            if (element) {\n                const resizeObserver = new ResizeObserver({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        cancelAnimationFrame(rAF);\n                        rAF = window.requestAnimationFrame(handleResize);\n                    }\n                }[\"useResizeObserver.useLayoutEffect\"]);\n                resizeObserver.observe(element);\n                return ({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        window.cancelAnimationFrame(rAF);\n                        resizeObserver.unobserve(element);\n                    }\n                })[\"useResizeObserver.useLayoutEffect\"];\n            }\n        }\n    }[\"useResizeObserver.useLayoutEffect\"], [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvdG9tc3V5cy9Eb2N1bWVudHMvR2l0SHViL1NheVdlYXRoZXJfcmlkZ2UvaGxzLXBsYXllci1hcHAvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jYWxsYmFjay1yZWYvc3JjL3VzZS1jYWxsYmFjay1yZWYudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUNhbGxiYWNrUmVmKGNhbGxiYWNrKSB7XG4gIGNvbnN0IGNhbGxiYWNrUmVmID0gUmVhY3QudXNlUmVmKGNhbGxiYWNrKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjYWxsYmFja1JlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gY2FsbGJhY2tSZWYuY3VycmVudD8uKC4uLmFyZ3MpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VDYWxsYmFja1JlZlxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy90b21zdXlzL0RvY3VtZW50cy9HaXRIdWIvU2F5V2VhdGhlcl9yaWRnZS9obHMtcGxheWVyLWFwcC9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2UtbGF5b3V0LWVmZmVjdC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZUxheW91dEVmZmVjdDIgPSBnbG9iYWxUaGlzPy5kb2N1bWVudCA/IFJlYWN0LnVzZUxheW91dEVmZmVjdCA6ICgpID0+IHtcbn07XG5leHBvcnQge1xuICB1c2VMYXlvdXRFZmZlY3QyIGFzIHVzZUxheW91dEVmZmVjdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ })

};
;