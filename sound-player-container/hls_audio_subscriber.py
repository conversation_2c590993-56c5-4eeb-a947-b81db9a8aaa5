#!/usr/bin/env python3
"""
HLS Audio Subscriber - Real-time HLS streaming from AudioEventManager

This module implements an AudioEventSubscriber that aggregates audio chunks
from the AudioEventManager into HLS-compatible segments and generates
M3U8 playlists for live streaming.
"""

import logging
import os
import threading
import time
from collections import deque
from pathlib import Path
from typing import List, Optional, Deque
import tempfile

import numpy as np
import ffmpeg
import m3u8

from audio_event_manager import AudioEventSubscriber, AudioEvent
from audio_config import AudioConfig
from hls_vps_uploader import HLSVPSUploader

logger = logging.getLogger(__name__)


class HLSAudioSubscriber(AudioEventSubscriber):
    """
    HLS Audio Subscriber that converts real-time audio chunks into HLS segments.
    
    This subscriber:
    1. Aggregates 1024-sample chunks into larger segments (1-2 seconds)
    2. Converts audio to HLS-compatible format using FFmpeg
    3. Generates and maintains M3U8 playlists
    4. Provides interface for uploading to VPS (mockup for now)
    """
    
    def __init__(self):
        """Initialize the HLS Audio Subscriber."""
        # Configuration from AudioConfig - simplified to 1 second segments
        self.segment_duration = 1.0  # Simplified to 1 second for lower latency
        self.playlist_size = AudioConfig.HLS_PLAYLIST_SIZE
        self.output_path = Path(AudioConfig.HLS_OUTPUT_PATH)
        self.audio_bitrate = AudioConfig.HLS_AUDIO_BITRATE
        self.audio_codec = AudioConfig.HLS_AUDIO_CODEC
        self.vps_upload_url = AudioConfig.API_BASE_URL
        self.vps_auth_token = AudioConfig.HLS_VPS_AUTH_TOKEN
        
        # Audio parameters (will be set from first AudioEvent)
        self.sample_rate: Optional[int] = None
        self.channels: Optional[int] = None
        
        # Segment aggregation - improved with sample-based timing
        # Limit buffer size to prevent memory growth (max ~10 seconds of audio)
        max_buffer_chunks = int((10.0 * 48000) / 1024)  # ~470 chunks for 10s at 48kHz/1024
        self.audio_buffer: Deque[np.ndarray] = deque(maxlen=max_buffer_chunks)
        self.buffer_timestamps: Deque[float] = deque(maxlen=max_buffer_chunks)
        self.samples_per_segment = 0  # Will be calculated based on sample rate
        self.current_samples = 0
        self.segment_start_time = None  # Track segment start for timestamps
        
        # HLS state
        self.segment_counter = 0
        self.playlist_segments: List[dict] = []
        self.is_streaming = False
        
        # Threading
        self._lock = threading.Lock()
        self._processing_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()

        # VPS uploader
        self.vps_uploader = HLSVPSUploader()

        # Create output directory
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"HLS Audio Subscriber initialized:")
        logger.info(f"  Segment duration: {self.segment_duration}s")
        logger.info(f"  Playlist size: {self.playlist_size}")
        logger.info(f"  Output path: {self.output_path}")
        logger.info(f"  Audio bitrate: {self.audio_bitrate}kbps")
        logger.info(f"  Audio codec: {self.audio_codec}")
    
    def on_start(self) -> None:
        """Called when audio processing starts."""
        logger.info("HLS streaming started")
        self.is_streaming = True
        self._stop_event.clear()
        
        # Start background processing thread for segment generation
        self._processing_thread = threading.Thread(target=self._segment_processor, daemon=True)
        self._processing_thread.start()
    
    def on_stop(self) -> None:
        """Called when audio processing stops."""
        logger.info("HLS streaming stopped")
        self.is_streaming = False
        self._stop_event.set()

        # Wait for processing thread to finish
        if self._processing_thread and self._processing_thread.is_alive():
            self._processing_thread.join(timeout=5.0)

        # Process any remaining audio in buffer
        with self._lock:
            if self.current_samples > 0:
                self._create_segment_from_buffer()

            # Clear buffers to free memory
            self.audio_buffer.clear()
            self.buffer_timestamps.clear()
            self.current_samples = 0

        # Stop VPS uploader
        self.vps_uploader.stop()
    
    def on_audio_chunk(self, event: AudioEvent) -> None:
        """Process incoming audio chunk."""
        with self._lock:
            # Initialize audio parameters from first event
            if self.sample_rate is None:
                self.sample_rate = event.sample_rate
                self.channels = event.channels
                self.samples_per_segment = int(self.sample_rate * self.segment_duration)
                self.segment_start_time = event.timestamp  # Track first segment start
                logger.info(f"Audio parameters initialized: {self.sample_rate}Hz, {self.channels}ch")
                logger.info(f"Samples per segment: {self.samples_per_segment} (1 second)")

            # Add chunk to buffer
            self.audio_buffer.append(event.chunk.copy())
            self.buffer_timestamps.append(event.timestamp)
            self.current_samples += len(event.chunk)

            # Check if we have enough samples for a segment
            if self.current_samples >= self.samples_per_segment:
                self._create_segment_from_buffer()
    
    def _create_segment_from_buffer(self) -> None:
        """Create an HLS segment from the current audio buffer."""
        if not self.audio_buffer:
            return
        
        try:
            # Concatenate audio chunks
            segment_audio = np.concatenate(list(self.audio_buffer))
            segment_start_time = self.buffer_timestamps[0] if self.buffer_timestamps else time.time()
            
            # Trim to exact segment length if we have more samples than needed
            if len(segment_audio) > self.samples_per_segment:
                segment_audio = segment_audio[:self.samples_per_segment]
                
                # Keep remaining samples for next segment
                remaining_samples = self.current_samples - self.samples_per_segment
                if remaining_samples > 0:
                    # Find where to split the buffer
                    samples_processed = 0
                    new_buffer = deque()
                    new_timestamps = deque()
                    
                    for i, chunk in enumerate(self.audio_buffer):
                        chunk_start = samples_processed
                        chunk_end = samples_processed + len(chunk)
                        
                        if chunk_end > self.samples_per_segment:
                            # This chunk spans the segment boundary
                            split_point = self.samples_per_segment - chunk_start
                            if split_point < len(chunk):
                                remaining_chunk = chunk[split_point:]
                                new_buffer.append(remaining_chunk)
                                new_timestamps.append(self.buffer_timestamps[i])
                            
                            # Add remaining chunks
                            for j in range(i + 1, len(self.audio_buffer)):
                                new_buffer.append(self.audio_buffer[j])
                                new_timestamps.append(self.buffer_timestamps[j])
                            break
                        
                        samples_processed += len(chunk)
                    
                    self.audio_buffer = new_buffer
                    self.buffer_timestamps = new_timestamps
                    self.current_samples = remaining_samples
                else:
                    self.audio_buffer.clear()
                    self.buffer_timestamps.clear()
                    self.current_samples = 0
            else:
                # Use all buffered audio
                self.audio_buffer.clear()
                self.buffer_timestamps.clear()
                self.current_samples = 0
            
            # Generate segment file
            segment_filename = f"segment_{self.segment_counter:06d}.ts"
            segment_path = self.output_path / segment_filename
            
            # Convert audio to HLS segment using FFmpeg
            self._convert_to_hls_segment(segment_audio, segment_path)
            
            # Add to playlist
            segment_info = {
                'filename': segment_filename,
                'path': segment_path,
                'duration': self.segment_duration,
                'timestamp': segment_start_time,
                'sequence': self.segment_counter
            }
            
            self.playlist_segments.append(segment_info)
            
            # Maintain playlist size (remove old segments)
            while len(self.playlist_segments) > self.playlist_size:
                old_segment = self.playlist_segments.pop(0)
                old_path = old_segment['path']
                if old_path.exists():
                    try:
                        old_path.unlink()
                        logger.debug(f"Removed old segment: {old_segment['filename']}")
                    except Exception as e:
                        logger.warning(f"Failed to remove old segment {old_segment['filename']}: {e}")
            
            # Update playlist
            self._update_playlist()

            # Upload to VPS
            self.vps_uploader.upload_segment(segment_path, segment_filename)
            self.vps_uploader.upload_playlist(self.output_path / 'playlist.m3u8')
            
            self.segment_counter += 1
            logger.debug(f"Created HLS segment: {segment_filename} ({len(segment_audio)} samples)")
            
        except Exception as e:
            logger.error(f"Error creating HLS segment: {e}")
    
    def _convert_to_hls_segment(self, audio_data: np.ndarray, output_path: Path) -> None:
        """Convert audio data to HLS segment using FFmpeg."""
        try:
            # Create temporary WAV file for FFmpeg input
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_wav:
                temp_wav_path = temp_wav.name
            
            try:
                # Write audio data to temporary WAV file
                import soundfile as sf
                sf.write(temp_wav_path, audio_data, self.sample_rate)
                
                # Convert to HLS segment using FFmpeg
                (
                    ffmpeg
                    .input(temp_wav_path)
                    .output(
                        str(output_path),
                        acodec=self.audio_codec,
                        audio_bitrate=f'{self.audio_bitrate}k',
                        f='mpegts',  # MPEG-TS format for HLS
                        avoid_negative_ts='make_zero'
                    )
                    .overwrite_output()
                    .run(quiet=True, capture_stdout=True)
                )
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_wav_path):
                    os.unlink(temp_wav_path)
                    
        except Exception as e:
            logger.error(f"Error converting audio to HLS segment: {e}")
            raise
    
    def _update_playlist(self) -> None:
        """Update the M3U8 playlist file."""
        try:
            playlist = m3u8.M3U8()
            playlist.version = 3
            playlist.target_duration = int(self.segment_duration) + 1  # Round up for safety
            playlist.media_sequence = max(0, self.segment_counter - len(self.playlist_segments))
            playlist.is_endlist = False  # Live stream
            playlist.allow_cache = False  # Disable caching for live streams
            
            # Add segments to playlist with timestamp URLs to prevent caching
            for segment_info in self.playlist_segments:
                # Add timestamp to prevent browser caching issues
                timestamp = int(segment_info['timestamp'] * 1000)
                uri_with_timestamp = f"{segment_info['filename']}?t={timestamp}"

                segment = m3u8.Segment(
                    uri=uri_with_timestamp,
                    duration=segment_info['duration']
                )
                playlist.segments.append(segment)
            
            # Write playlist file
            playlist_path = self.output_path / 'playlist.m3u8'
            with open(playlist_path, 'w') as f:
                f.write(playlist.dumps())
            
            logger.debug(f"Updated playlist with {len(self.playlist_segments)} segments")
            
        except Exception as e:
            logger.error(f"Error updating playlist: {e}")
    

    
    def _segment_processor(self) -> None:
        """Background thread for processing segments."""
        logger.info("HLS segment processor started")
        
        while not self._stop_event.is_set():
            try:
                # Check if we need to create a segment due to timeout
                # This ensures segments are created even if audio stops
                with self._lock:
                    if (self.current_samples > 0 and 
                        self.buffer_timestamps and 
                        time.time() - self.buffer_timestamps[0] > self.segment_duration * 1.5):
                        logger.debug("Creating segment due to timeout")
                        self._create_segment_from_buffer()
                
                # Sleep briefly to avoid busy waiting
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in segment processor: {e}")
        
        logger.info("HLS segment processor stopped")
    
    def get_playlist_url(self) -> str:
        """Get the URL/path to the M3U8 playlist."""
        return str(self.output_path / 'playlist.m3u8')
    
    def get_stats(self) -> dict:
        """Get streaming statistics."""
        with self._lock:
            stats = {
                'is_streaming': self.is_streaming,
                'segment_counter': self.segment_counter,
                'playlist_segments': len(self.playlist_segments),
                'current_buffer_samples': self.current_samples,
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'output_path': str(self.output_path),
                'vps_uploader': self.vps_uploader.get_stats(),
                'upload_queue_size': self.vps_uploader.get_queue_size()
            }
            return stats
