# Memory Optimization and Audio Overflow Fixes

## Overview

This document summarizes the changes made to address RAM usage growth and audio overflow issues in the sound-player-container.

## Issues Identified

1. **Unbounded Queues**: Several queues had no size limits, allowing unlimited memory growth
2. **Audio Callback Overflow**: High chunk sizes (>2000) caused audio input overflow
3. **Buffer Accumulation**: Audio buffers in various components could grow without bounds
4. **Missing Cleanup**: Some cleanup routines were incomplete
5. **File Handle Leaks**: Potential file handle leaks in upload operations

## Changes Made

### 1. Audio Event Manager (`audio_event_manager.py`)

**Problem**: Unbounded audio queue could accumulate chunks if processing couldn't keep up.

**Fixes**:
- Added bounded queue with maxsize=500 (~10 seconds of buffering at 48kHz/1024)
- Non-blocking queue puts with drop counting
- Queue clearing on stop
- Statistics tracking for dropped chunks
- Improved logging of queue status

### 2. HLS Audio Subscriber (`hls_audio_subscriber.py`)

**Problem**: Audio buffers could grow indefinitely.

**Fixes**:
- Added maxlen to deques based on reasonable time limits (~10 seconds)
- Buffer clearing on stop
- Improved memory cleanup

### 3. Recording Controller (`recording_controller.py`)

**Problem**: Pre-roll buffer and recording buffers could accumulate.

**Fixes**:
- Dynamic pre-roll buffer sizing based on duration
- Periodic pre-roll buffer trimming
- Improved buffer clearing in reset

### 4. HLS VPS Uploader (`hls_vps_uploader.py`)

**Problem**: Upload queue could grow without bounds.

**Fixes**:
- Bounded upload queue (maxsize=100)
- Non-blocking queue operations with drop counting
- Queue clearing on stop
- Statistics for dropped uploads

### 5. API Upload Manager (`api_upload_manager.py`)

**Problem**: Upload queue and file handle management issues.

**Fixes**:
- Bounded upload queue (maxsize=50)
- Proper file handle management with try/finally
- Queue clearing on shutdown
- Drop counting and logging

### 6. Memory Monitor (`memory_monitor.py`) - NEW

**Purpose**: Track memory usage and detect potential leaks.

**Features**:
- Periodic memory usage logging (every 5 minutes)
- Memory growth tracking
- Peak memory detection
- Final statistics on shutdown
- Process memory monitoring using psutil

### 7. Audio Configuration (`audio_config.py`)

**Problem**: No validation for problematic chunk sizes.

**Fixes**:
- Added warning for chunk sizes > 2000 (known to cause overflow)
- Improved configuration validation

### 8. Main Application (`main.py`)

**Integration**:
- Added memory monitoring startup/shutdown
- Integrated memory monitor into cleanup process

## Configuration Recommendations

### Optimal Chunk Size
- **Recommended**: 960-1024 samples
- **Avoid**: >2000 samples (causes audio overflow)
- **Current setting**: 960 (good choice)

### Memory Monitoring
- Logs memory usage every 5 minutes
- Tracks growth rate and peak usage
- Provides final statistics on shutdown

## Expected Results

1. **Stable Memory Usage**: Bounded queues prevent unlimited growth
2. **No Audio Overflow**: Proper chunk size validation and queue management
3. **Better Monitoring**: Memory usage tracking helps identify issues early
4. **Improved Cleanup**: Thorough resource cleanup on shutdown
5. **Drop Statistics**: Visibility into when system is overloaded

## Monitoring

The system now logs:
- Memory usage every 5 minutes
- Queue drop statistics when they occur
- Final memory statistics on shutdown
- Audio callback status warnings

## Testing Recommendations

1. **Monitor Memory**: Watch for steady memory growth over time
2. **Check Logs**: Look for queue full warnings or dropped chunks
3. **Test High Load**: Verify system handles sustained audio input
4. **Chunk Size Testing**: Confirm 960 chunk size works without overflow
5. **Long Running**: Test for several hours to verify stability

## Rollback Plan

If issues occur, the changes are isolated and can be reverted:
1. Remove memory monitor integration from main.py
2. Revert queue size limits (remove maxsize parameters)
3. Remove non-blocking queue operations
4. Remove buffer size limits (maxlen parameters)

All changes maintain backward compatibility and don't affect core functionality.
