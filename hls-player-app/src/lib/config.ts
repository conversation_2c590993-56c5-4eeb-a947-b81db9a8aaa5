/**
 * Configuration for the HLS Player Application
 */

export interface AppConfig {
  api: {
    baseUrl: string
    timeout: number
  }
  player: {
    defaultStationId: string
    autoPlay: boolean
    autoRefresh: boolean
    refreshIntervals: {
      status: number
      stats: number
    }
  }
  hls: {
    debug: boolean
    enableWorker: boolean
    lowLatencyMode: boolean
    maxBufferLength: number
    liveSyncDurationCount: number
    liveMaxLatencyDurationCount: number
    liveDurationInfinity: boolean
    manifestLoadingTimeOut: number
    manifestLoadingMaxRetry: number
    manifestLoadingRetryDelay: number
    levelLoadingTimeOut: number
    fragLoadingTimeOut: number
  }
  ui: {
    theme: 'light' | 'dark' | 'system'
    showAdvancedStats: boolean
  }
}

// Default configuration
const defaultConfig: AppConfig = {
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://awosnew.skytraces.com',
    timeout: 10000
  },
  player: {
    defaultStationId: process.env.NEXT_PUBLIC_STATION_ID || '4FL5',
    autoPlay: false,
    autoRefresh: true,
    refreshIntervals: {
      status: 5000, // 5 seconds
      stats: 2000   // 2 seconds
    }
  },
  hls: {
    debug: process.env.NODE_ENV === 'development',
    enableWorker: true,
    lowLatencyMode: true,
    // Simplified robust settings for live streaming
    maxBufferLength: 4, // Keep buffer small for low latency
    liveSyncDurationCount: 1, // Stay very close to live edge
    liveMaxLatencyDurationCount: 2, // Maximum allowed latency
    liveDurationInfinity: true,
    // Frequent playlist fetching for robustness
    manifestLoadingTimeOut: 3000,
    manifestLoadingMaxRetry: 5,
    manifestLoadingRetryDelay: 500,
    levelLoadingTimeOut: 5000,
    fragLoadingTimeOut: 8000
  },
  ui: {
    theme: 'system',
    showAdvancedStats: false
  }
}

// Environment-specific overrides
const getEnvironmentConfig = (): Partial<AppConfig> => {
  const env = process.env.NODE_ENV

  switch (env) {
    case 'development':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: true
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: true
        }
      }
    
    case 'production':
      return {
        hls: {
          ...defaultConfig.hls,
          debug: false
        },
        ui: {
          ...defaultConfig.ui,
          showAdvancedStats: false
        }
      }
    
    default:
      return {}
  }
}

// Merge configurations
export const config: AppConfig = {
  ...defaultConfig,
  ...getEnvironmentConfig()
}

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = config.api.baseUrl.replace(/\/$/, '') // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseUrl}${cleanEndpoint}`
}

export const getHLSPlaylistUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/playlist.m3u8`)
}

export const getHLSStatusUrl = (stationId: string): string => {
  return getApiUrl(`/hls/${stationId}/status`)
}

export const getHLSSegmentUrl = (stationId: string, segmentName: string): string => {
  return getApiUrl(`/hls/${stationId}/${segmentName}`)
}

export const getRecordingsStationId = (): string => {
  // Hardcode the station ID as requested - the API expects "4FL5_122900"
  return "4FL5_122900"
}

// Removed complex adaptive configuration - using simplified robust settings instead

// Validation functions
export const validateStationId = (stationId: string): boolean => {
  return /^[A-Z0-9_]+$/.test(stationId)
}

export const validateApiUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Type guards
export const isValidConfig = (config: unknown): config is AppConfig => {
  if (!config || typeof config !== 'object') return false

  const cfg = config as Record<string, unknown>

  return !!(
    cfg.api &&
    typeof cfg.api === 'object' &&
    cfg.api !== null &&
    typeof (cfg.api as Record<string, unknown>).baseUrl === 'string' &&
    typeof (cfg.api as Record<string, unknown>).timeout === 'number' &&
    cfg.player &&
    typeof cfg.player === 'object' &&
    cfg.player !== null &&
    typeof (cfg.player as Record<string, unknown>).defaultStationId === 'string' &&
    typeof (cfg.player as Record<string, unknown>).autoPlay === 'boolean' &&
    cfg.hls &&
    typeof cfg.hls === 'object' &&
    cfg.hls !== null &&
    typeof (cfg.hls as Record<string, unknown>).debug === 'boolean'
  )
}

export default config
